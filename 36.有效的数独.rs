/*
 * @lc app=leetcode.cn id=36 lang=rust
 *
 * [36] 有效的数独
 */

// @lc code=start
impl Solution {
    pub fn is_valid_sudoku(board: Vec<Vec<char>>) -> bool {
        let mut row = vec![0; 9];
        let mut col = vec![0; 9];
        let mut box_ = vec![0; 9];
        for i in 0..9 {
            for j in 0..9 {
                if board[i][j] != '.' {
                    let num = board[i][j] as u8 - '0' as u8;
                    let box_idx = (i / 3) * 3 + j / 3;
                    if row[i] & (1 << num) != 0 || col[j] & (1 << num) != 0 || box_[box_idx] & (1 << num) != 0 {
                        return false;
                    }
                    row[i] |= 1 << num;
                    col[j] |= 1 << num;
                    box_[box_idx] |= 1 << num;
                }
            }
        }
        true
    }
}
// @lc code=end

