/*
 * @lc app=leetcode.cn id=34 lang=rust
 *
 * [34] 在排序数组中查找元素的第一个和最后一个位置
 */

// @lc code=start
impl Solution {
    pub fn search_range(nums: Vec<i32>, target: i32) -> Vec<i32> {
        let lt = Self::find(&nums, target, true);
        let rt = Self::find(&nums, target, false) - 1;
        if (lt <= rt && rt < nums.len() && nums[lt] == target && nums[rt] == target) {
            return [lt as i32, rt as i32].to_vec();
        } else {
            return [-1, -1].to_vec();
        }
    }
    pub fn find (nums: &Vec<i32>, target: i32, isLeft: bool) -> usize {
        // 如果中 >= target， 那么取左
        // isLeft要优先取左边
        let mut left: i32 = 0;
        let mut right = nums.len() as i32 - 1;
        let mut ans: usize = nums.len();
        while left <= right {
            let mid = (left + (right - left) / 2) as usize;
            if (isLeft && nums[mid] >= target ) || nums[mid] > target {
                right = (mid - 1) as i32;
                ans = mid;
            } else {
                left = (mid + 1) as i32;
            }
        }
        ans
    }
}


// @lc code=end

