impl Solution {
    pub fn reverse(x: i32) -> i32 {
        let mut ret : i32 = 0;
        let mut p = x;
        while p.abs() > 0 {
            //ret = ret * 10 + p % 10;
            ret = match ret.checked_mul(10) {
                Some(v)=> v,
                None => {
                    return 0;
                }
            };
            ret = match ret.checked_add(p % 10) {
                Some(v)=> v,
                None => {
                    return 0;
                }
            };
            p = p / 10;
        }
        ret
    }
}
