/*
 * @lc app=leetcode.cn id=49 lang=rust
 *
 * [49] 字母异位词分组
 */

use std::collections::HashMap;

// @lc code=start
struct Solution;

impl Solution {
    pub fn group_anagrams(strs: Vec<String>) -> Vec<Vec<String>> {
        let mut ans = HashMap::new();
        for s in strs {
            let mut key: Vec<char> = s.chars().collect();
            key.sort();
            let key_string: String = key.into_iter().collect();
            ans.entry(key_string).or_insert(vec![]).push(s);
        }
        ans.into_values().collect()
    }
}
