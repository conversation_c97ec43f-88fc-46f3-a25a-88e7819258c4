/*
 * @lc app=leetcode.cn id=49 lang=rust
 *
 * [49] 字母异位词分组
 */

// @lc code=start
impl Solution {
    pub fn group_anagrams(strs: Vec<String>) -> Vec<Vec<String>> {
        let mut ans = HashMap::new();
        for s in strs {
            let mut key = s.clone();
            key.sort();
            ans.entry(key).or_insert(vec![]).push(s);
        }
        ans.into_values().collect()
    }
}
// @lc code=end

