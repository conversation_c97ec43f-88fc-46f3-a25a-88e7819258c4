/*
 * @lc app=leetcode.cn id=102 lang=rust
 *
 * [102] 二叉树的层序遍历
 */

// @lc code=start
// Definition for a binary tree node.
// #[derive(Debug, PartialEq, Eq)]
// pub struct TreeNode {
//   pub val: i32,
//   pub left: Option<Rc<RefCell<TreeNode>>>,
//   pub right: Option<Rc<RefCell<TreeNode>>>,
// }
//
// impl TreeNode {
//   #[inline]
//   pub fn new(val: i32) -> Self {
//     TreeNode {
//       val,
//       left: None,
//       right: None
//     }
//   }
// }
use std::rc::Rc;
use std::cell::RefCell;
impl Solution {
    pub fn level_order(root: Option<Rc<RefCell<TreeNode>>>) -> Vec<Vec<i32>> {
        let mut ans = vec![];
        let mut queue = vec![];
        if let Some(x) = root {
            queue.push(x);
        }
        while !queue.is_empty() {
            let len = queue.len();
            let mut slice = vec![];
            for item in (0..len) {
                
                let mut x = queue.remove(0);
                let mut x = x.borrow_mut();
                slice.push(x.val);
                if let Some(left) = x.left.take(){
                    queue.push(left);
                }
                if let Some(right) = x.right.take(){
                    queue.push(right);
                }
            } 
            ans.push(slice);
        }
        ans
    }
}
// @lc code=end

