/*
 * @lc app=leetcode.cn id=64 lang=rust
 *
 * [64] 最小路径和
 */

// @lc code=start
impl Solution {
    pub fn min_path_sum(grid: Vec<Vec<i32>>) -> i32 {
        // DP[x, y] = Min(DP[x-1, y], DP[x, y-1]) + Grid[x, y]
        let m = grid.len() ;
        let n = grid[0].len();
        let mut dp = vec![0; n];
        dp[0] = grid[0][0];

        // 初始化第一行，只能从左边来
        for j in (1..n){
            dp[j] = grid[0][j] + dp[j - 1];
        }

        for i in (1..m) {
            dp[0] += grid[i][0];
            for j in (1..n) {
                dp[j] = grid[i][j] + dp[j].min(dp[j-1]);
            }
        } 
        dp[n - 1]
    }
}
// @lc code=end

