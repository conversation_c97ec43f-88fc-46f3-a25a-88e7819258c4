/*
 * @lc app=leetcode.cn id=45 lang=rust
 *
 * [45] 跳跃游戏 II
 */

// @lc code=start
impl Solution {
    pub fn jump(nums: Vec<i32>) -> i32 {
        let mut max_pos = 0;
        let mut end = 0;
        let mut steps = 0;
        for i in 0..nums.len() - 1 {
            if max_pos >= i {
                max_pos = max_pos.max(i + nums[i] as usize);
                if i == end {
                    end = max_pos;
                    steps += 1;
                }
            }
        }
        steps
    }
}
// @lc code=end

