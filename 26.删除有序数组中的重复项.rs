/*
 * @lc app=leetcode.cn id=26 lang=rust
 *
 * [26] 删除有序数组中的重复项
 */

// @lc code=start
impl Solution {
    pub fn remove_duplicates(nums: &mut Vec<i32>) -> i32 {
        let mut p = 0;
        let mut q = 1;
        while q < nums.len()  {
            if nums[p] != nums[q] {
                nums[p + 1] = nums[q];
                p += 1;
            }
            q += 1;
        }
        p as i32 + 1
    }
}
// @lc code=end

