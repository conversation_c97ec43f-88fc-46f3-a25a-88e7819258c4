/*
 * @lc app=leetcode.cn id=20 lang=rust
 *
 * [20] 有效的括号
 */

// @lc code=start
impl Solution {
    pub fn is_valid(s: String) -> bool {
         if s.len() % 2 != 0 { // s 长度必须是偶数
            return false;
        }
        let mut stack = vec!['0'];
        for item in s.chars() {
            match item {
                '(' | '[' | '{' => stack.push(item),
                ')'=> {
                    if stack.pop().unwrap() != '(' {
                        return false
                    }
                },
                ']'=> {
                    if stack.pop().unwrap() != '[' {
                        return false
                    }
                },
                '}'=> {
                    if stack.pop().unwrap() != '{' {
                        return false
                    }
                },
                _=> ()
            }
        };
        stack.len() == 1
    }
}
// @lc code=end

